package com.dcai.aixg.pro.task;

import org.apache.commons.lang3.StringUtils;

import com.ejuetc.commons.base.exception.BusinessException;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class WriteCreateHouseBaseInfoPO {

//	@Schema(name = "cityName", description = "城市名")
//    private String cityName;
//	
//	@Schema(name = "regionName", description = "区域名")
//	private String regionName;
//
//    @Schema(name = "areaName", description = "板块名")
//    private String areaName;
	
    @Schema(name = "estateName", description = "小区名称")
    private String estateName;
	
//    @Schema(name = "estateId", description = "小区id")
//    private String estateId;
	
    @Schema(name = "layoutName", description = "户型")
    private String layoutName;
	
    @Schema(name = "buildingArea", description = "建筑面积")
    private String buildingArea;
	
    @Schema(name = "buildName", description = "楼栋")
    private String buildName;
	
    @Schema(name = "unitName", description = "单元")
    private String unitName;
	
    @Schema(name = "roomNum", description = "房号")
    private String roomNum;
	
    @Schema(name = "orient", description = "朝向")
    private String orient;
    
    public void checkParams() {
    	if (StringUtils.isBlank(estateName)) throw new BusinessException("bc.cpm.aixg.1012", "小区名称");
    	if (StringUtils.isBlank(layoutName)) throw new BusinessException("bc.cpm.aixg.1012", "户型");
    	if (StringUtils.isBlank(buildingArea)) throw new BusinessException("bc.cpm.aixg.1012", "建筑面积");
    }

}
