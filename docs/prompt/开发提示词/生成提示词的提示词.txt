豆包提示词:从图片内容转换为markdown文档，其中的图表以表格数据输出，如果图表数据无法准确解析请生成模拟数据（不要用“...”或文字描述替代），并将图表样式备注在数据表格后，要求内容完整无遗漏


(中文!中文!中文!答复)生成新的提示词文件,达到与[docs/prompt/7_价值评测/proms/prom_价值测评_s3_md2json.md]相同的效果:
1,markdown_content部分:使用文件[docs/prompt/2_价值对比/source/提示词输入内容.md]里的内容
2,json_structure_definition部分:可以照搬[prom_价值测评_s3_md2json.md]里的内容
3,json_template部分:参考界面效果[docs/prompt/2_价值对比/source/界面内容提取.md],给出对应的JSON模板
4,system_prompt和user_prompt部分:参考[prom_价值测评_s3_md2json.md]相关部分,完成markdown_content+json_template=>json_report的转换
注意事项:
1,最终生成的提示词文件,要求其能产生图文并茂的JSON输出,因此需要充分挖掘数据内涵,尽量利用CHART控件呈现数据趋势对比等信息,且选择差异化图表类型以呈现界面多样性;
2,生成的文本内容具有一定的摘要性,因此需要限定输出文字数量:单独输出的文字段落,字数不能超过60字;列表项单项文字内容不能超过30字;
3,单位信息的呈现:单个图表中若数据单位相同则呈现在图表标题上,不要在每个数据分类中重复呈现单位信息;当图表中同一组数据(含标题),大部分超过或接近10000时,转换单位为万(显示单位并保留两位小数)
4,图表中由于两组数据量级差别大会造成柱状图呈现效果不佳,请更换为混合图表使用柱状和折线图呈现两组数据;
5,对于饼状图,限定一下分类数量上限为5,多余5的部分合并呈现为其他;
6,对于非数字类型的对比项内容(往往以表格控件输出),需将对比后的推荐项设置recommended=true,以便呈现推荐信息;


(中文!中文!中文!答复)基于提示词[prom_价值对比.md]生成了结果[output_价值对比.json],请修改提示词以解决以下问题:
1,输出中111~119行都是"豪华"装修,却对其中一个标记["recommended": true],这不符合常理,请优化推荐逻辑;
2,参考目标输出[target.json],优化提示词以输出更加丰富的数据内容,也可以将原数据中有价值的数据组织成图表添加到合适位置进行输出;

(中文!中文!中文!答复)批量修改以下提示词文件,增加以下生成规则:对于饼状图,限定一下分类数量上限为5,多余5的部分合并呈现为其他;
- [docs/prompt/1_政策解读专家/proms/prom_政策解读_md2json.md]
- [docs/prompt/2_房产生活专家/proms/prom_房产生活_md2json.md]
- [docs/prompt/3_房源专家/proms/prom_房源专家_md2json.md]
- [docs/prompt/4_区域专家/proms/prom_区域专家_md2json.md]
- [docs/prompt/5_学区专家/proms/prom_学区专家_md2json.md]
- [docs/prompt/6_小区专家/proms/prom_小区专家_md2json.md]
- [docs/prompt/7_价值评测/proms/prom_价值测评_md2json.md]